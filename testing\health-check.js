#!/usr/bin/env node

/**
 * ATMA Backend Health Check Utility
 * 
 * Checks the health of all ATMA backend services before running load tests.
 * Provides detailed service status and recommendations.
 */

const axios = require('axios');

// Service Configuration
const SERVICES = {
    'API Gateway': {
        url: 'http://localhost:3000',
        healthEndpoint: '/health',
        critical: true
    },
    'Auth Service': {
        url: 'http://localhost:3001',
        healthEndpoint: '/health',
        critical: true
    },
    'Archive Service': {
        url: 'http://localhost:3002',
        healthEndpoint: '/health',
        critical: true
    },
    'Assessment Service': {
        url: 'http://localhost:3003',
        healthEndpoint: '/health',
        critical: true
    },
    'Notification Service': {
        url: 'http://localhost:3005',
        healthEndpoint: '/health',
        critical: true
    }
};

class HealthChecker {
    constructor() {
        this.results = {};
        this.startTime = Date.now();
    }

    async checkService(name, config) {
        const startTime = Date.now();
        try {
            const response = await axios.get(
                `${config.url}${config.healthEndpoint}`,
                { timeout: 10000 }
            );
            
            const responseTime = Date.now() - startTime;
            
            this.results[name] = {
                status: 'healthy',
                responseTime: `${responseTime}ms`,
                statusCode: response.status,
                data: response.data,
                critical: config.critical
            };
            
            return true;
        } catch (error) {
            const responseTime = Date.now() - startTime;
            
            this.results[name] = {
                status: 'unhealthy',
                responseTime: `${responseTime}ms`,
                error: error.message,
                statusCode: error.response?.status || 0,
                critical: config.critical
            };
            
            return false;
        }
    }

    async checkAllServices() {
        console.log('🔍 Checking ATMA Backend Services Health...\n');
        
        const promises = Object.entries(SERVICES).map(([name, config]) => 
            this.checkService(name, config)
        );
        
        await Promise.all(promises);
        
        this.generateReport();
        return this.isSystemHealthy();
    }

    isSystemHealthy() {
        const criticalServices = Object.entries(this.results)
            .filter(([name, result]) => result.critical);
        
        const unhealthyCritical = criticalServices
            .filter(([name, result]) => result.status === 'unhealthy');
        
        return unhealthyCritical.length === 0;
    }

    generateReport() {
        const totalTime = Date.now() - this.startTime;
        
        console.log('═'.repeat(70));
        console.log('🏥 ATMA BACKEND HEALTH CHECK REPORT');
        console.log('═'.repeat(70));
        
        console.log(`\n⏱️  Total Check Time: ${totalTime}ms\n`);
        
        // Service Status Overview
        console.log('📊 SERVICE STATUS OVERVIEW');
        console.log('─'.repeat(70));
        
        Object.entries(this.results).forEach(([name, result]) => {
            const statusIcon = result.status === 'healthy' ? '✅' : '❌';
            const criticalLabel = result.critical ? '[CRITICAL]' : '[OPTIONAL]';
            
            console.log(`${statusIcon} ${name} ${criticalLabel}`);
            console.log(`   Status: ${result.status.toUpperCase()}`);
            console.log(`   Response Time: ${result.responseTime}`);
            
            if (result.status === 'healthy') {
                console.log(`   Status Code: ${result.statusCode}`);
                if (result.data) {
                    console.log(`   Service Info: ${JSON.stringify(result.data).substring(0, 100)}...`);
                }
            } else {
                console.log(`   Error: ${result.error}`);
                if (result.statusCode > 0) {
                    console.log(`   Status Code: ${result.statusCode}`);
                }
            }
            console.log('');
        });
        
        // Summary
        const healthyServices = Object.values(this.results)
            .filter(result => result.status === 'healthy').length;
        const totalServices = Object.keys(this.results).length;
        const healthPercentage = ((healthyServices / totalServices) * 100).toFixed(1);
        
        console.log('📈 SUMMARY');
        console.log('─'.repeat(30));
        console.log(`Healthy Services: ${healthyServices}/${totalServices} (${healthPercentage}%)`);
        
        const criticalHealthy = Object.values(this.results)
            .filter(result => result.critical && result.status === 'healthy').length;
        const totalCritical = Object.values(this.results)
            .filter(result => result.critical).length;
        
        console.log(`Critical Services: ${criticalHealthy}/${totalCritical} healthy`);
        
        // Recommendations
        console.log('\n💡 RECOMMENDATIONS');
        console.log('─'.repeat(30));
        
        if (this.isSystemHealthy()) {
            console.log('🎉 All critical services are healthy!');
            console.log('✅ System is ready for load testing.');
            
            // Performance recommendations
            const slowServices = Object.entries(this.results)
                .filter(([name, result]) => {
                    const responseTime = parseInt(result.responseTime);
                    return responseTime > 1000;
                });
            
            if (slowServices.length > 0) {
                console.log('\n⚠️  Performance Warnings:');
                slowServices.forEach(([name, result]) => {
                    console.log(`   - ${name}: Slow response (${result.responseTime})`);
                });
                console.log('   Consider reducing load test concurrency.');
            }
        } else {
            console.log('❌ System is NOT ready for load testing!');
            console.log('\n🔧 Required Actions:');
            
            Object.entries(this.results)
                .filter(([name, result]) => result.critical && result.status === 'unhealthy')
                .forEach(([name, result]) => {
                    console.log(`   - Fix ${name}: ${result.error}`);
                });
        }
        
        console.log('\n🏁 HEALTH CHECK COMPLETED');
        console.log('═'.repeat(70));
    }

    async checkServiceEndpoints() {
        console.log('\n🔍 Checking Additional Service Endpoints...\n');
        
        const additionalChecks = [
            {
                name: 'API Gateway - Auth Routes',
                url: 'http://localhost:3000/api/auth/health',
                critical: true
            },
            {
                name: 'API Gateway - Assessment Routes',
                url: 'http://localhost:3000/api/assessment/health',
                critical: true
            },
            {
                name: 'API Gateway - Archive Routes',
                url: 'http://localhost:3000/api/archive/health',
                critical: true
            }
        ];
        
        for (const check of additionalChecks) {
            const startTime = Date.now();
            try {
                const response = await axios.get(check.url, { timeout: 5000 });
                const responseTime = Date.now() - startTime;
                
                console.log(`✅ ${check.name}: OK (${responseTime}ms)`);
            } catch (error) {
                const responseTime = Date.now() - startTime;
                console.log(`❌ ${check.name}: FAILED (${responseTime}ms) - ${error.message}`);
            }
        }
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    const helpFlag = args.includes('--help') || args.includes('-h');
    const detailedFlag = args.includes('--detailed') || args.includes('-d');
    const jsonFlag = args.includes('--json');
    
    if (helpFlag) {
        console.log(`
ATMA Backend Health Check Utility

Usage: node health-check.js [options]

Options:
  --detailed, -d    Run detailed endpoint checks
  --json           Output results in JSON format
  --help, -h       Show this help message

Examples:
  node health-check.js                    # Basic health check
  node health-check.js --detailed         # Detailed health check
  node health-check.js --json             # JSON output
        `);
        return;
    }
    
    const checker = new HealthChecker();
    
    try {
        const isHealthy = await checker.checkAllServices();
        
        if (detailedFlag) {
            await checker.checkServiceEndpoints();
        }
        
        if (jsonFlag) {
            console.log('\n📄 JSON Report:');
            console.log(JSON.stringify({
                timestamp: new Date().toISOString(),
                systemHealthy: isHealthy,
                services: checker.results
            }, null, 2));
        }
        
        // Exit with appropriate code
        process.exit(isHealthy ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Health check failed:', error.message);
        process.exit(1);
    }
}

// Export for use in other scripts
module.exports = { HealthChecker, SERVICES };

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
