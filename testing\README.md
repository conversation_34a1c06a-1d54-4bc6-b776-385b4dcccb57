# ATMA Backend E2E Load Testing Suite

Comprehensive end-to-end load testing suite untuk ATMA (AI-Driven Talent Mapping Assessment) backend system. Testing ini mensimulasikan 50 user concurrent yang melakukan full user journey dari registrasi hingga penghapusan akun.

## 🎯 Test Flow Overview

Testing ini mensimulasikan skenario real-world dengan 50 user yang melakukan:

1. **Register** - Registrasi dengan data random yang valid
2. **Login** - Login bersamaan dengan kredensial yang baru dibuat
3. **Update Profile** - Update data profil mereka
4. **Submit Assessment** - Submit assessment dengan data random
5. **WebSocket Connection** - Koneksi ke notification service dan menunggu assessment selesai
6. **Check Results** - Mengecek hasil assessment mereka
7. **Delete Account** - Menghapus akun mereka (logout)

## 📋 Prerequisites

### System Requirements
- Node.js >= 16.0.0
- NPM atau Yarn
- ATMA Backend services running:
  - API Gateway (port 3000)
  - Auth Service (port 3001)
  - Archive Service (port 3002)
  - Assessment Service (port 3003)
  - Notification Service (port 3005)

### Service Dependencies
Pastikan semua service ATMA backend sudah berjalan sebelum menjalankan testing:

```bash
# Check service health
curl http://localhost:3000/health
curl http://localhost:3005/health
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd testing
npm install
```

### 2. Run Default Test (50 users)
```bash
npm test
```

### 3. Run with Custom Configuration
```bash
# Test dengan 10 users (small load)
npm run test:small

# Test dengan 25 users (medium load)
npm run test:medium

# Test dengan 100 users (large load)
npm run test:large

# Custom configuration
node e2e-load-test.js --users 75 --timeout 60000
```

## ⚙️ Configuration Options

### Command Line Arguments

| Argument | Description | Default | Example |
|----------|-------------|---------|---------|
| `--users` | Number of concurrent users | 50 | `--users 100` |
| `--timeout` | HTTP timeout (ms) | 30000 | `--timeout 60000` |
| `--ws-timeout` | WebSocket timeout (ms) | 300000 | `--ws-timeout 600000` |
| `--base-url` | API Gateway URL | http://localhost:3000 | `--base-url http://api.atma.com` |
| `--ws-url` | WebSocket URL | http://localhost:3005 | `--ws-url http://ws.atma.com` |
| `--help` | Show help message | - | `--help` |

### Example Commands
```bash
# Test dengan 200 concurrent users
node e2e-load-test.js --users 200

# Test dengan timeout yang lebih lama
node e2e-load-test.js --users 50 --timeout 60000 --ws-timeout 600000

# Test dengan custom URLs (production)
node e2e-load-test.js --base-url https://api.atma.com --ws-url https://ws.atma.com

# Show help
node e2e-load-test.js --help
```

## 📊 Report Output

### Console Output
Testing akan menampilkan real-time progress dan comprehensive report di console:

```
🚀 Starting ATMA E2E Load Test with 50 concurrent users

Configuration:
- Base URL: http://localhost:3000
- WebSocket URL: http://localhost:3005
- Concurrent Users: 50
- Timeout: 30000ms
- WebSocket Timeout: 300000ms

⏳ Running tests for all 50 users concurrently...

[User 1] Starting full test flow...
[User 1] ✅ Registered successfully
[User 1] ✅ Logged in successfully
...

✅ All tests completed in 45.67 seconds

═══════════════════════════════════════════════════════════════════════════════
📊 ATMA E2E LOAD TEST REPORT
═══════════════════════════════════════════════════════════════════════════════

📈 OVERALL SUMMARY
──────────────────────────────────────────────────────
Total Duration: 45.67s
Total Users: 50
Successful Tests: 48 (96.00%)
Failed Tests: 2 (4.00%)
Overall Throughput: 1.09 users/sec

🎯 STAGE-BY-STAGE PERFORMANCE
────────────────────────────────────────────────────────────────────────────────

REGISTER:
  Success Rate: 100.00% (50/50)
  Response Time: Min=245ms, Avg=456ms, Max=1234ms
  Percentiles: P95=987ms, P99=1156ms
  Throughput: 1.09 req/sec

LOGIN:
  Success Rate: 98.00% (49/50)
  Response Time: Min=123ms, Avg=234ms, Max=567ms
  Percentiles: P95=456ms, P99=534ms
  Throughput: 1.07 req/sec

...
```

### JSON Report File
Setiap test run akan menghasilkan detailed JSON report file:

```
📄 Detailed report saved to: load-test-report-2024-01-21T10-30-45-123Z.json
```

File report berisi:
- Timestamp dan configuration
- Detailed metrics untuk setiap stage
- Raw test results
- Success/failure analysis

## 📈 Metrics Collected

### Performance Metrics
- **Response Time**: Min, Max, Average, P95, P99
- **Success Rate**: Percentage dan count
- **Throughput**: Requests per second
- **Duration**: Total test duration

### Stage-Specific Metrics
1. **Register**: User registration performance
2. **Login**: Authentication performance
3. **Update Profile**: Profile update performance
4. **Submit Assessment**: Assessment submission performance
5. **WebSocket Connection**: WebSocket connection dan authentication
6. **Assessment Completion**: Time to receive completion notification
7. **Check Results**: Result retrieval performance
8. **Delete Account**: Account deletion (logout) performance

### Error Analysis
- Failed request details
- Error categorization
- Bottleneck identification
- Performance insights

## 🔧 Troubleshooting

### Common Issues

#### 1. Connection Refused
```
Error: connect ECONNREFUSED 127.0.0.1:3000
```
**Solution**: Pastikan API Gateway berjalan di port 3000
```bash
cd api-gateway
npm start
```

#### 2. WebSocket Connection Failed
```
Error: WebSocket connection failed
```
**Solution**: Pastikan Notification Service berjalan di port 3005
```bash
cd notification-service
npm start
```

#### 3. Authentication Timeout
```
Error: Authentication timeout
```
**Solution**: Increase WebSocket timeout atau check service performance
```bash
node e2e-load-test.js --ws-timeout 600000
```

#### 4. High Failure Rate
**Possible Causes**:
- Database connection issues
- Service overload
- Network latency
- Rate limiting

**Solutions**:
- Reduce concurrent users: `--users 25`
- Increase timeouts: `--timeout 60000`
- Check service logs
- Monitor system resources

### Debug Mode
Enable debug logging untuk troubleshooting:

```bash
# Enable debug untuk Socket.IO
DEBUG=socket.io-client:socket node e2e-load-test.js

# Enable debug untuk HTTP requests
DEBUG=axios node e2e-load-test.js
```

## 🎛️ Advanced Usage

### Custom Test Data
Modify `generateRandomUser()` dan `generateAssessmentData()` functions untuk custom test data:

```javascript
const generateRandomUser = () => {
    // Custom user generation logic
};
```

### Custom Metrics
Extend `MetricsCollector` class untuk additional metrics:

```javascript
class CustomMetricsCollector extends MetricsCollector {
    recordCustomMetric(name, value) {
        // Custom metric logic
    }
}
```

### Integration dengan CI/CD
```bash
# Exit dengan error code jika success rate < 95%
node e2e-load-test.js --users 50 | grep "Success Rate" | awk '{if($3 < 95) exit 1}'
```

## 📝 Test Data

### Generated User Data
- **Email**: `testuser{uuid}@atma-test.com`
- **Password**: `TestPass{uuid}123!`
- **Username**: `testuser{uuid}`
- **Full Name**: `Test User {uuid}`
- **School ID**: Random 1-10

### Generated Assessment Data
- **RIASEC**: 6 dimensions with random scores 0-100
- **OCEAN**: 5 personality traits with random scores 0-100
- **VIA-IS**: 24 character strengths with random scores 0-100

## 🔒 Security Considerations

### Test Data Cleanup
- Test users menggunakan email domain `@atma-test.com`
- Accounts di-delete setelah testing (logout)
- No sensitive data dalam test payload

### Rate Limiting
- Respect API rate limits
- Use appropriate delays jika diperlukan
- Monitor service health during testing

## 📞 Support

Untuk issues atau questions:
1. Check service health endpoints
2. Review console logs dan error messages
3. Check generated JSON report untuk detailed analysis
4. Monitor system resources (CPU, memory, network)

## 🔄 Continuous Integration

Example GitHub Actions workflow:

```yaml
name: Load Testing
on: [push, pull_request]
jobs:
  load-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
        working-directory: ./testing
      - run: npm run test:small
        working-directory: ./testing
```
