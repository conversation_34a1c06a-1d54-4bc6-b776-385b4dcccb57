#!/usr/bin/env node

/**
 * ATMA Load Testing Scenarios Runner
 * 
 * Runs predefined testing scenarios with different configurations
 * and provides comparative analysis between scenarios.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { <PERSON>Checker } = require('./health-check');

// Predefined Test Scenarios
const SCENARIOS = {
    'smoke': {
        name: 'Smoke Test',
        description: 'Quick validation with minimal load',
        users: 5,
        timeout: 30000,
        wsTimeout: 180000
    },
    'light': {
        name: 'Light Load Test',
        description: 'Light load testing with 10 concurrent users',
        users: 10,
        timeout: 30000,
        wsTimeout: 300000
    },
    'normal': {
        name: 'Normal Load Test',
        description: 'Standard load testing with 25 concurrent users',
        users: 25,
        timeout: 45000,
        wsTimeout: 300000
    },
    'medium': {
        name: 'Medium Load Test',
        description: 'Medium load testing with 50 concurrent users',
        users: 50,
        timeout: 60000,
        wsTimeout: 300000
    },
    'heavy': {
        name: 'Heavy Load Test',
        description: 'Heavy load testing with 100 concurrent users',
        users: 100,
        timeout: 90000,
        wsTimeout: 600000
    },
    'stress': {
        name: 'Stress Test',
        description: 'Stress testing with 200 concurrent users',
        users: 200,
        timeout: 120000,
        wsTimeout: 900000
    },
    'endurance': {
        name: 'Endurance Test',
        description: 'Endurance testing with 75 users for extended period',
        users: 75,
        timeout: 180000,
        wsTimeout: 1200000
    }
};

class ScenarioRunner {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }

    async runScenario(scenarioKey, scenario) {
        console.log(`\n${'='.repeat(80)}`);
        console.log(`🎯 Running ${scenario.name}`);
        console.log(`📝 ${scenario.description}`);
        console.log(`👥 Users: ${scenario.users} | ⏱️  Timeout: ${scenario.timeout}ms | 🔌 WS Timeout: ${scenario.wsTimeout}ms`);
        console.log(`${'='.repeat(80)}\n`);

        const startTime = Date.now();
        
        try {
            const result = await this.executeLoadTest(scenario);
            const duration = Date.now() - startTime;
            
            const scenarioResult = {
                scenario: scenarioKey,
                name: scenario.name,
                config: scenario,
                duration: duration,
                success: result.success,
                report: result.report,
                timestamp: new Date().toISOString()
            };
            
            this.results.push(scenarioResult);
            
            console.log(`\n✅ ${scenario.name} completed in ${(duration / 1000).toFixed(2)}s`);
            
            if (result.success) {
                console.log(`📊 Success Rate: ${result.report.summary.successRate || 'N/A'}`);
                console.log(`🚀 Throughput: ${result.report.summary.overallThroughput || 'N/A'}`);
            } else {
                console.log(`❌ Test failed: ${result.error}`);
            }
            
            return scenarioResult;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            
            const scenarioResult = {
                scenario: scenarioKey,
                name: scenario.name,
                config: scenario,
                duration: duration,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
            
            this.results.push(scenarioResult);
            
            console.log(`\n❌ ${scenario.name} failed after ${(duration / 1000).toFixed(2)}s`);
            console.log(`Error: ${error.message}`);
            
            return scenarioResult;
        }
    }

    async executeLoadTest(scenario) {
        return new Promise((resolve, reject) => {
            const args = [
                'e2e-load-test.js',
                '--users', scenario.users.toString(),
                '--timeout', scenario.timeout.toString(),
                '--ws-timeout', scenario.wsTimeout.toString()
            ];
            
            const child = spawn('node', args, {
                cwd: __dirname,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            let stdout = '';
            let stderr = '';
            
            child.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                process.stdout.write(output); // Real-time output
            });
            
            child.stderr.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                process.stderr.write(output); // Real-time error output
            });
            
            child.on('close', (code) => {
                if (code === 0) {
                    try {
                        // Try to parse report from stdout
                        const reportMatch = stdout.match(/📊 ATMA E2E LOAD TEST REPORT[\s\S]*?🏁 TEST COMPLETED/);
                        const report = reportMatch ? this.parseReportFromOutput(stdout) : null;
                        
                        resolve({
                            success: true,
                            report: report,
                            stdout: stdout,
                            stderr: stderr
                        });
                    } catch (parseError) {
                        resolve({
                            success: true,
                            report: null,
                            stdout: stdout,
                            stderr: stderr,
                            parseError: parseError.message
                        });
                    }
                } else {
                    reject(new Error(`Load test exited with code ${code}. Error: ${stderr}`));
                }
            });
            
            child.on('error', (error) => {
                reject(new Error(`Failed to start load test: ${error.message}`));
            });
        });
    }

    parseReportFromOutput(output) {
        // Simple parsing of key metrics from console output
        const report = { summary: {}, stages: {} };
        
        // Extract overall summary
        const successfulMatch = output.match(/Successful Tests: (\d+) \(([^)]+)\)/);
        if (successfulMatch) {
            report.summary.successfulTests = parseInt(successfulMatch[1]);
            report.summary.successRate = successfulMatch[2];
        }
        
        const throughputMatch = output.match(/Overall Throughput: ([^\n]+)/);
        if (throughputMatch) {
            report.summary.overallThroughput = throughputMatch[1];
        }
        
        const durationMatch = output.match(/Total Duration: ([^\n]+)/);
        if (durationMatch) {
            report.summary.totalDuration = durationMatch[1];
        }
        
        return report;
    }

    async runMultipleScenarios(scenarioKeys) {
        console.log(`\n🚀 Starting ATMA Load Testing Scenarios`);
        console.log(`📋 Scenarios to run: ${scenarioKeys.join(', ')}`);
        console.log(`⏰ Started at: ${new Date().toISOString()}\n`);
        
        // Health check before starting
        console.log('🏥 Performing health check before testing...');
        const healthChecker = new HealthChecker();
        const isHealthy = await healthChecker.checkAllServices();
        
        if (!isHealthy) {
            throw new Error('System health check failed. Please fix services before running tests.');
        }
        
        console.log('✅ Health check passed. Starting scenarios...\n');
        
        // Run scenarios sequentially
        for (const scenarioKey of scenarioKeys) {
            const scenario = SCENARIOS[scenarioKey];
            if (!scenario) {
                console.log(`⚠️  Unknown scenario: ${scenarioKey}. Skipping...`);
                continue;
            }
            
            await this.runScenario(scenarioKey, scenario);
            
            // Wait between scenarios to let system recover
            if (scenarioKeys.indexOf(scenarioKey) < scenarioKeys.length - 1) {
                console.log('\n⏳ Waiting 30 seconds before next scenario...\n');
                await this.sleep(30000);
            }
        }
        
        // Generate comparative report
        this.generateComparativeReport();
    }

    generateComparativeReport() {
        const totalTime = Date.now() - this.startTime;
        
        console.log(`\n${'='.repeat(100)}`);
        console.log('📊 COMPARATIVE SCENARIO ANALYSIS');
        console.log(`${'='.repeat(100)}`);
        
        console.log(`\n⏱️  Total Testing Time: ${(totalTime / 1000 / 60).toFixed(2)} minutes\n`);
        
        // Summary table
        console.log('📈 SCENARIO COMPARISON');
        console.log('─'.repeat(100));
        console.log('| Scenario      | Users | Duration | Success Rate | Throughput    | Status |');
        console.log('|---------------|-------|----------|--------------|---------------|--------|');
        
        this.results.forEach(result => {
            const duration = `${(result.duration / 1000).toFixed(1)}s`;
            const successRate = result.report?.summary?.successRate || 'N/A';
            const throughput = result.report?.summary?.overallThroughput || 'N/A';
            const status = result.success ? '✅ PASS' : '❌ FAIL';
            
            console.log(`| ${result.name.padEnd(13)} | ${result.config.users.toString().padEnd(5)} | ${duration.padEnd(8)} | ${successRate.padEnd(12)} | ${throughput.padEnd(13)} | ${status} |`);
        });
        
        // Performance insights
        console.log('\n💡 PERFORMANCE INSIGHTS');
        console.log('─'.repeat(50));
        
        const successfulResults = this.results.filter(r => r.success);
        
        if (successfulResults.length > 0) {
            // Find best performing scenario
            const bestThroughput = successfulResults.reduce((best, current) => {
                const currentThroughput = parseFloat(current.report?.summary?.overallThroughput || '0');
                const bestThroughput = parseFloat(best.report?.summary?.overallThroughput || '0');
                return currentThroughput > bestThroughput ? current : best;
            });
            
            console.log(`🏆 Best Throughput: ${bestThroughput.name} (${bestThroughput.report?.summary?.overallThroughput})`);
            
            // Find breaking point
            const failedResults = this.results.filter(r => !r.success);
            if (failedResults.length > 0) {
                const firstFailure = failedResults[0];
                console.log(`⚠️  Breaking Point: ${firstFailure.name} (${firstFailure.config.users} users)`);
            }
        }
        
        // Recommendations
        console.log('\n🎯 RECOMMENDATIONS');
        console.log('─'.repeat(30));
        
        const passedScenarios = this.results.filter(r => r.success).length;
        const totalScenarios = this.results.length;
        
        if (passedScenarios === totalScenarios) {
            console.log('🎉 All scenarios passed! System is performing well.');
            console.log('💪 Consider running stress tests with higher loads.');
        } else if (passedScenarios > totalScenarios / 2) {
            console.log('✅ Most scenarios passed. Good system performance.');
            console.log('🔧 Investigate failed scenarios for optimization opportunities.');
        } else {
            console.log('⚠️  Many scenarios failed. System needs optimization.');
            console.log('🔍 Focus on fixing critical performance bottlenecks.');
        }
        
        // Save detailed report
        this.saveDetailedReport();
        
        console.log('\n🏁 SCENARIO TESTING COMPLETED');
        console.log(`${'='.repeat(100)}`);
    }

    saveDetailedReport() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `scenario-comparison-${timestamp}.json`;
        
        const report = {
            timestamp: new Date().toISOString(),
            totalDuration: Date.now() - this.startTime,
            scenarios: this.results,
            summary: {
                totalScenarios: this.results.length,
                passedScenarios: this.results.filter(r => r.success).length,
                failedScenarios: this.results.filter(r => !r.success).length
            }
        };
        
        try {
            fs.writeFileSync(filename, JSON.stringify(report, null, 2));
            console.log(`📄 Detailed scenario report saved to: ${filename}`);
        } catch (error) {
            console.error(`Failed to save scenario report: ${error.message}`);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    const helpFlag = args.includes('--help') || args.includes('-h');
    
    if (helpFlag) {
        console.log(`
ATMA Load Testing Scenarios Runner

Usage: node test-scenarios.js [scenarios...] [options]

Available Scenarios:
  smoke      - Quick validation (5 users)
  light      - Light load (10 users)
  normal     - Normal load (25 users)
  medium     - Medium load (50 users)
  heavy      - Heavy load (100 users)
  stress     - Stress test (200 users)
  endurance  - Endurance test (75 users, extended timeout)

Special Commands:
  all        - Run all scenarios
  progressive - Run progressive load (smoke → light → normal → medium)
  stress-test - Run stress scenarios (medium → heavy → stress)

Options:
  --help, -h    Show this help message

Examples:
  node test-scenarios.js smoke light          # Run smoke and light tests
  node test-scenarios.js progressive          # Run progressive load test
  node test-scenarios.js all                  # Run all scenarios
  node test-scenarios.js medium heavy         # Run medium and heavy tests
        `);
        return;
    }
    
    let scenarioKeys = args.filter(arg => !arg.startsWith('--'));
    
    // Handle special commands
    if (scenarioKeys.includes('all')) {
        scenarioKeys = Object.keys(SCENARIOS);
    } else if (scenarioKeys.includes('progressive')) {
        scenarioKeys = ['smoke', 'light', 'normal', 'medium'];
    } else if (scenarioKeys.includes('stress-test')) {
        scenarioKeys = ['medium', 'heavy', 'stress'];
    }
    
    // Default to normal scenario if none specified
    if (scenarioKeys.length === 0) {
        scenarioKeys = ['normal'];
    }
    
    // Validate scenarios
    const invalidScenarios = scenarioKeys.filter(key => !SCENARIOS[key]);
    if (invalidScenarios.length > 0) {
        console.error(`❌ Invalid scenarios: ${invalidScenarios.join(', ')}`);
        console.error('Use --help to see available scenarios');
        process.exit(1);
    }
    
    const runner = new ScenarioRunner();
    
    try {
        await runner.runMultipleScenarios(scenarioKeys);
        
        // Exit with success if all scenarios passed
        const allPassed = runner.results.every(r => r.success);
        process.exit(allPassed ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Scenario testing failed:', error.message);
        process.exit(1);
    }
}

// Export for use in other scripts
module.exports = { ScenarioRunner, SCENARIOS };

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
