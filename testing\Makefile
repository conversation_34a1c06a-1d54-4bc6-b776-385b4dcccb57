# ATMA Backend Testing Makefile
# Provides convenient commands for running various testing scenarios

.PHONY: help install health test scenarios clean

# Default target
help:
	@echo "ATMA Backend Testing Commands"
	@echo "============================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  make install          Install dependencies"
	@echo "  make health           Check service health"
	@echo "  make health-detailed  Detailed health check"
	@echo ""
	@echo "Testing Commands:"
	@echo "  make test             Run default load test (50 users)"
	@echo "  make smoke            Run smoke test (5 users)"
	@echo "  make light            Run light load test (10 users)"
	@echo "  make normal           Run normal load test (25 users)"
	@echo "  make medium           Run medium load test (50 users)"
	@echo "  make heavy            Run heavy load test (100 users)"
	@echo "  make stress           Run stress test (200 users)"
	@echo ""
	@echo "Scenario Commands:"
	@echo "  make scenarios        Run default scenarios"
	@echo "  make scenarios-all    Run all scenarios"
	@echo "  make scenarios-prog   Run progressive scenarios"
	@echo "  make scenarios-stress Run stress test scenarios"
	@echo ""
	@echo "Utility Commands:"
	@echo "  make clean            Clean up test reports"
	@echo "  make logs             Show recent test logs"
	@echo "  make help             Show this help message"
	@echo ""
	@echo "Custom Commands:"
	@echo "  make test-custom USERS=75 TIMEOUT=60000"
	@echo "  make test-prod BASE_URL=https://api.atma.com"

# Setup Commands
install:
	@echo "📦 Installing dependencies..."
	npm install
	@echo "✅ Dependencies installed successfully"

# Health Check Commands
health:
	@echo "🏥 Checking service health..."
	node health-check.js

health-detailed:
	@echo "🏥 Running detailed health check..."
	node health-check.js --detailed

health-json:
	@echo "🏥 Running health check with JSON output..."
	node health-check.js --json

# Basic Testing Commands
test:
	@echo "🚀 Running default load test (50 users)..."
	node e2e-load-test.js

smoke:
	@echo "💨 Running smoke test (5 users)..."
	node test-scenarios.js smoke

light:
	@echo "🪶 Running light load test (10 users)..."
	node test-scenarios.js light

normal:
	@echo "📊 Running normal load test (25 users)..."
	node test-scenarios.js normal

medium:
	@echo "⚖️  Running medium load test (50 users)..."
	node test-scenarios.js medium

heavy:
	@echo "🏋️  Running heavy load test (100 users)..."
	node test-scenarios.js heavy

stress:
	@echo "💥 Running stress test (200 users)..."
	node test-scenarios.js stress

endurance:
	@echo "🏃 Running endurance test (75 users, extended timeout)..."
	node test-scenarios.js endurance

# Scenario Commands
scenarios:
	@echo "🎯 Running default scenarios..."
	node test-scenarios.js normal

scenarios-all:
	@echo "🎯 Running all scenarios..."
	node test-scenarios.js all

scenarios-prog:
	@echo "🎯 Running progressive scenarios..."
	node test-scenarios.js progressive

scenarios-stress:
	@echo "🎯 Running stress test scenarios..."
	node test-scenarios.js stress-test

# Custom Testing Commands
test-custom:
	@echo "🎛️  Running custom test..."
	@if [ -z "$(USERS)" ]; then \
		echo "Usage: make test-custom USERS=50 [TIMEOUT=30000] [WS_TIMEOUT=300000]"; \
		exit 1; \
	fi
	@ARGS="--users $(USERS)"; \
	if [ -n "$(TIMEOUT)" ]; then ARGS="$$ARGS --timeout $(TIMEOUT)"; fi; \
	if [ -n "$(WS_TIMEOUT)" ]; then ARGS="$$ARGS --ws-timeout $(WS_TIMEOUT)"; fi; \
	if [ -n "$(BASE_URL)" ]; then ARGS="$$ARGS --base-url $(BASE_URL)"; fi; \
	if [ -n "$(WS_URL)" ]; then ARGS="$$ARGS --ws-url $(WS_URL)"; fi; \
	echo "Running: node e2e-load-test.js $$ARGS"; \
	node e2e-load-test.js $$ARGS

test-prod:
	@echo "🌐 Running production test..."
	@if [ -z "$(BASE_URL)" ]; then \
		echo "Usage: make test-prod BASE_URL=https://api.atma.com [WS_URL=https://ws.atma.com] [USERS=25]"; \
		exit 1; \
	fi
	@USERS=$${USERS:-25}; \
	WS_URL=$${WS_URL:-$$(echo $(BASE_URL) | sed 's/api\./ws./')}; \
	echo "Testing production environment:"; \
	echo "  API URL: $(BASE_URL)"; \
	echo "  WebSocket URL: $$WS_URL"; \
	echo "  Users: $$USERS"; \
	node e2e-load-test.js --users $$USERS --base-url $(BASE_URL) --ws-url $$WS_URL --timeout 60000

# Utility Commands
clean:
	@echo "🧹 Cleaning up test reports..."
	@find . -name "load-test-report-*.json" -type f -delete 2>/dev/null || true
	@find . -name "scenario-comparison-*.json" -type f -delete 2>/dev/null || true
	@echo "✅ Test reports cleaned"

logs:
	@echo "📋 Recent test reports:"
	@ls -la *.json 2>/dev/null | head -10 || echo "No test reports found"

# Development Commands
dev-setup: install health
	@echo "🛠️  Development environment setup complete"
	@echo "Run 'make smoke' to verify everything works"

# CI/CD Commands
ci-test: health smoke light normal
	@echo "✅ CI/CD test suite completed"

# Quick Commands
quick: health smoke
	@echo "⚡ Quick validation completed"

full: health scenarios-all
	@echo "🎯 Full testing suite completed"

# Monitoring Commands
monitor:
	@echo "📊 Starting real-time monitoring..."
	@echo "Press Ctrl+C to stop"
	@while true; do \
		clear; \
		echo "ATMA Services Health Status - $$(date)"; \
		echo "=========================================="; \
		node health-check.js --json | jq -r '.services | to_entries[] | "\(.key): \(.value.status)"' 2>/dev/null || node health-check.js; \
		echo ""; \
		echo "Press Ctrl+C to stop monitoring..."; \
		sleep 30; \
	done

# Help for specific commands
help-custom:
	@echo "Custom Test Examples:"
	@echo "  make test-custom USERS=100 TIMEOUT=60000"
	@echo "  make test-custom USERS=50 WS_TIMEOUT=600000"
	@echo "  make test-prod BASE_URL=https://api.atma.com USERS=25"

help-scenarios:
	@echo "Available Scenarios:"
	@echo "  smoke     - 5 users, quick validation"
	@echo "  light     - 10 users, light load"
	@echo "  normal    - 25 users, normal load"
	@echo "  medium    - 50 users, medium load"
	@echo "  heavy     - 100 users, heavy load"
	@echo "  stress    - 200 users, stress test"
	@echo "  endurance - 75 users, extended timeout"

# Validation Commands
validate: health
	@echo "✅ System validation completed"

# Performance Benchmarking
benchmark: scenarios-prog
	@echo "📈 Performance benchmark completed"
	@echo "Check scenario-comparison-*.json for detailed results"

# Emergency Commands
emergency-test: health smoke
	@echo "🚨 Emergency validation completed"

# All-in-one Commands
everything: install health scenarios-all clean
	@echo "🎉 Complete testing cycle finished"
