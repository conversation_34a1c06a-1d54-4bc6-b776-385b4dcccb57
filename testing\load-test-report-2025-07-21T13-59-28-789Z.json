{"timestamp": "2025-07-21T13:59:28.790Z", "configuration": {"BASE_URL": "http://localhost:3000", "WEBSOCKET_URL": "http://localhost:3005", "CONCURRENT_USERS": 10, "TIMEOUT": 30000, "WEBSOCKET_TIMEOUT": 300000}, "summary": {"totalDuration": "0.69s", "totalUsers": 10, "overallThroughput": "14.51 users/sec", "successfulTests": 0, "failedTests": 10, "successRate": "0.00%"}, "stages": {"register": {"successRate": "100.00%", "successes": 10, "failures": 0, "responseTime": {"min": "230ms", "max": "441ms", "avg": "359ms", "p95": "441ms", "p99": "441ms"}, "throughput": "14.51 req/sec"}, "login": {"successRate": "100.00%", "successes": 10, "failures": 0, "responseTime": {"min": "146ms", "max": "274ms", "avg": "196ms", "p95": "274ms", "p99": "274ms"}, "throughput": "14.51 req/sec"}, "updateProfile": {"successRate": "0.00%", "successes": 0, "failures": 10, "responseTime": {"min": "24ms", "max": "75ms", "avg": "46ms", "p95": "75ms", "p99": "75ms"}, "throughput": "14.51 req/sec"}, "submitAssessment": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "websocketConnection": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "assessmentCompletion": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "checkResults": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "deleteAccount": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}}, "rawResults": [{"status": "fulfilled", "value": {"success": false, "userId": 1, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 1 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 2, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 7 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 3, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 9 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 4, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 8 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 5, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 10 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 6, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 6 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 7, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 2 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 8, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 10 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 9, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 5 does not exist\",\"details\":{}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 10, "error": "Profile update failed: {\"success\":false,\"error\":{\"code\":\"INVALID_SCHOOL_ID\",\"message\":\"School with ID 9 does not exist\",\"details\":{}}}"}}]}