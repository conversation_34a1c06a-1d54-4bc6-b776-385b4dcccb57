#!/usr/bin/env node

/**
 * ATMA Real-time System Monitor
 * 
 * Monitors system health and performance in real-time during load testing.
 * Provides live updates on service status, response times, and system metrics.
 */

const { HealthChecker } = require('./health-check');
const axios = require('axios');

class SystemMonitor {
    constructor(options = {}) {
        this.interval = options.interval || 10000; // 10 seconds default
        this.maxHistory = options.maxHistory || 50;
        this.history = [];
        this.isRunning = false;
        this.startTime = Date.now();
        
        // Service URLs
        this.services = {
            'API Gateway': 'http://localhost:3000',
            'Auth Service': 'http://localhost:3001',
            'Archive Service': 'http://localhost:3002',
            'Assessment Service': 'http://localhost:3003',
            'Notification Service': 'http://localhost:3005'
        };
    }

    async start() {
        console.log('🔍 Starting ATMA System Monitor...\n');
        console.log(`📊 Monitoring interval: ${this.interval / 1000}s`);
        console.log(`📈 History limit: ${this.maxHistory} entries`);
        console.log(`⏰ Started at: ${new Date().toISOString()}\n`);
        console.log('Press Ctrl+C to stop monitoring\n');
        
        this.isRunning = true;
        
        // Initial health check
        await this.performHealthCheck();
        
        // Start monitoring loop
        this.monitoringLoop();
        
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            this.stop();
        });
    }

    async monitoringLoop() {
        while (this.isRunning) {
            await this.sleep(this.interval);
            
            if (this.isRunning) {
                await this.performHealthCheck();
                this.displayRealTimeStatus();
            }
        }
    }

    async performHealthCheck() {
        const timestamp = new Date().toISOString();
        const checkStartTime = Date.now();
        
        const healthData = {
            timestamp,
            services: {},
            summary: {
                totalServices: 0,
                healthyServices: 0,
                avgResponseTime: 0
            }
        };

        const promises = Object.entries(this.services).map(async ([name, url]) => {
            const startTime = Date.now();
            try {
                const response = await axios.get(`${url}/health`, { timeout: 5000 });
                const responseTime = Date.now() - startTime;
                
                healthData.services[name] = {
                    status: 'healthy',
                    responseTime: responseTime,
                    statusCode: response.status,
                    data: response.data
                };
                
                return responseTime;
            } catch (error) {
                const responseTime = Date.now() - startTime;
                
                healthData.services[name] = {
                    status: 'unhealthy',
                    responseTime: responseTime,
                    error: error.message,
                    statusCode: error.response?.status || 0
                };
                
                return responseTime;
            }
        });

        const responseTimes = await Promise.all(promises);
        
        // Calculate summary
        healthData.summary.totalServices = Object.keys(this.services).length;
        healthData.summary.healthyServices = Object.values(healthData.services)
            .filter(service => service.status === 'healthy').length;
        healthData.summary.avgResponseTime = Math.round(
            responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        );
        healthData.summary.checkDuration = Date.now() - checkStartTime;
        
        // Add to history
        this.history.push(healthData);
        
        // Limit history size
        if (this.history.length > this.maxHistory) {
            this.history.shift();
        }
    }

    displayRealTimeStatus() {
        // Clear screen
        console.clear();
        
        const latest = this.history[this.history.length - 1];
        const uptime = Date.now() - this.startTime;
        
        console.log('═'.repeat(80));
        console.log('📊 ATMA REAL-TIME SYSTEM MONITOR');
        console.log('═'.repeat(80));
        
        console.log(`⏰ Current Time: ${new Date().toLocaleString()}`);
        console.log(`🕐 Monitor Uptime: ${this.formatDuration(uptime)}`);
        console.log(`📈 Data Points: ${this.history.length}/${this.maxHistory}`);
        console.log(`🔄 Check Duration: ${latest.summary.checkDuration}ms\n`);
        
        // Service Status
        console.log('🏥 SERVICE STATUS');
        console.log('─'.repeat(80));
        
        Object.entries(latest.services).forEach(([name, service]) => {
            const statusIcon = service.status === 'healthy' ? '✅' : '❌';
            const responseTime = `${service.responseTime}ms`;
            
            console.log(`${statusIcon} ${name.padEnd(20)} | ${service.status.toUpperCase().padEnd(10)} | ${responseTime.padEnd(8)} | ${service.statusCode || 'N/A'}`);
        });
        
        // Summary Statistics
        console.log('\n📊 SUMMARY STATISTICS');
        console.log('─'.repeat(50));
        console.log(`Healthy Services: ${latest.summary.healthyServices}/${latest.summary.totalServices} (${((latest.summary.healthyServices / latest.summary.totalServices) * 100).toFixed(1)}%)`);
        console.log(`Average Response Time: ${latest.summary.avgResponseTime}ms`);
        
        // Trend Analysis (if we have enough history)
        if (this.history.length >= 3) {
            this.displayTrendAnalysis();
        }
        
        // Performance Alerts
        this.checkPerformanceAlerts(latest);
        
        console.log('\n─'.repeat(80));
        console.log('Press Ctrl+C to stop monitoring | Next update in 10 seconds');
    }

    displayTrendAnalysis() {
        console.log('\n📈 TREND ANALYSIS (Last 10 checks)');
        console.log('─'.repeat(50));
        
        const recentHistory = this.history.slice(-10);
        
        // Calculate trends
        const avgResponseTimes = recentHistory.map(h => h.summary.avgResponseTime);
        const healthyRates = recentHistory.map(h => 
            (h.summary.healthyServices / h.summary.totalServices) * 100
        );
        
        const responseTimeTrend = this.calculateTrend(avgResponseTimes);
        const healthTrend = this.calculateTrend(healthyRates);
        
        console.log(`Response Time Trend: ${this.formatTrend(responseTimeTrend)} (${avgResponseTimes[avgResponseTimes.length - 1]}ms current)`);
        console.log(`Health Rate Trend: ${this.formatTrend(healthTrend)} (${healthyRates[healthyRates.length - 1].toFixed(1)}% current)`);
        
        // Service-specific trends
        Object.keys(this.services).forEach(serviceName => {
            const serviceTimes = recentHistory.map(h => h.services[serviceName]?.responseTime || 0);
            const trend = this.calculateTrend(serviceTimes);
            const trendIcon = trend > 0 ? '📈' : trend < 0 ? '📉' : '➡️';
            
            console.log(`${serviceName}: ${trendIcon} ${serviceTimes[serviceTimes.length - 1]}ms`);
        });
    }

    calculateTrend(values) {
        if (values.length < 2) return 0;
        
        const recent = values.slice(-3);
        const older = values.slice(-6, -3);
        
        if (older.length === 0) return 0;
        
        const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
        const olderAvg = older.reduce((sum, val) => sum + val, 0) / older.length;
        
        return ((recentAvg - olderAvg) / olderAvg) * 100;
    }

    formatTrend(trend) {
        if (Math.abs(trend) < 1) return '➡️  Stable';
        if (trend > 0) return `📈 +${trend.toFixed(1)}%`;
        return `📉 ${trend.toFixed(1)}%`;
    }

    checkPerformanceAlerts(latest) {
        const alerts = [];
        
        // Check for unhealthy services
        Object.entries(latest.services).forEach(([name, service]) => {
            if (service.status === 'unhealthy') {
                alerts.push(`🚨 ${name} is unhealthy: ${service.error}`);
            }
        });
        
        // Check for slow response times
        Object.entries(latest.services).forEach(([name, service]) => {
            if (service.responseTime > 5000) {
                alerts.push(`⚠️  ${name} slow response: ${service.responseTime}ms`);
            }
        });
        
        // Check overall health rate
        const healthRate = (latest.summary.healthyServices / latest.summary.totalServices) * 100;
        if (healthRate < 100) {
            alerts.push(`⚠️  System health degraded: ${healthRate.toFixed(1)}%`);
        }
        
        if (alerts.length > 0) {
            console.log('\n🚨 PERFORMANCE ALERTS');
            console.log('─'.repeat(50));
            alerts.forEach(alert => console.log(alert));
        }
    }

    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    stop() {
        console.log('\n\n⏹️  Stopping monitor...');
        this.isRunning = false;
        
        // Generate final report
        this.generateFinalReport();
        
        console.log('👋 Monitor stopped. Goodbye!');
        process.exit(0);
    }

    generateFinalReport() {
        if (this.history.length === 0) return;
        
        console.log('\n📊 MONITORING SESSION SUMMARY');
        console.log('═'.repeat(50));
        
        const totalDuration = Date.now() - this.startTime;
        const totalChecks = this.history.length;
        
        console.log(`Session Duration: ${this.formatDuration(totalDuration)}`);
        console.log(`Total Health Checks: ${totalChecks}`);
        console.log(`Check Frequency: ${(totalChecks / (totalDuration / 1000 / 60)).toFixed(1)} checks/min`);
        
        // Calculate overall statistics
        const allHealthRates = this.history.map(h => 
            (h.summary.healthyServices / h.summary.totalServices) * 100
        );
        const allResponseTimes = this.history.map(h => h.summary.avgResponseTime);
        
        console.log(`\nHealth Rate: Min=${Math.min(...allHealthRates).toFixed(1)}%, Max=${Math.max(...allHealthRates).toFixed(1)}%, Avg=${(allHealthRates.reduce((a, b) => a + b, 0) / allHealthRates.length).toFixed(1)}%`);
        console.log(`Response Time: Min=${Math.min(...allResponseTimes)}ms, Max=${Math.max(...allResponseTimes)}ms, Avg=${Math.round(allResponseTimes.reduce((a, b) => a + b, 0) / allResponseTimes.length)}ms`);
        
        // Save monitoring data
        this.saveMonitoringData();
    }

    saveMonitoringData() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `monitoring-session-${timestamp}.json`;
        
        const report = {
            sessionInfo: {
                startTime: new Date(this.startTime).toISOString(),
                endTime: new Date().toISOString(),
                duration: Date.now() - this.startTime,
                totalChecks: this.history.length,
                interval: this.interval
            },
            history: this.history,
            services: this.services
        };
        
        try {
            const fs = require('fs');
            fs.writeFileSync(filename, JSON.stringify(report, null, 2));
            console.log(`📄 Monitoring data saved to: ${filename}`);
        } catch (error) {
            console.error(`Failed to save monitoring data: ${error.message}`);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    const helpFlag = args.includes('--help') || args.includes('-h');
    
    if (helpFlag) {
        console.log(`
ATMA Real-time System Monitor

Usage: node monitor.js [options]

Options:
  --interval <ms>     Monitoring interval in milliseconds (default: 10000)
  --history <count>   Maximum history entries to keep (default: 50)
  --help, -h          Show this help message

Examples:
  node monitor.js                        # Default monitoring
  node monitor.js --interval 5000        # Check every 5 seconds
  node monitor.js --interval 30000 --history 100  # Custom settings
        `);
        return;
    }
    
    // Parse options
    const options = {};
    
    for (let i = 0; i < args.length; i += 2) {
        const flag = args[i];
        const value = args[i + 1];
        
        switch (flag) {
            case '--interval':
                options.interval = parseInt(value) || 10000;
                break;
            case '--history':
                options.maxHistory = parseInt(value) || 50;
                break;
        }
    }
    
    const monitor = new SystemMonitor(options);
    await monitor.start();
}

// Export for use in other scripts
module.exports = { SystemMonitor };

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
