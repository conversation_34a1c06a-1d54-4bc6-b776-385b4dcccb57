{"timestamp": "2025-07-21T14:01:10.959Z", "configuration": {"BASE_URL": "http://localhost:3000", "WEBSOCKET_URL": "http://localhost:3005", "CONCURRENT_USERS": 10, "TIMEOUT": 30000, "WEBSOCKET_TIMEOUT": 300000}, "summary": {"totalDuration": "0.68s", "totalUsers": 10, "overallThroughput": "14.66 users/sec", "successfulTests": 0, "failedTests": 10, "successRate": "0.00%"}, "stages": {"register": {"successRate": "100.00%", "successes": 10, "failures": 0, "responseTime": {"min": "110ms", "max": "231ms", "avg": "159ms", "p95": "231ms", "p99": "231ms"}, "throughput": "14.66 req/sec"}, "login": {"successRate": "100.00%", "successes": 10, "failures": 0, "responseTime": {"min": "117ms", "max": "192ms", "avg": "155ms", "p95": "192ms", "p99": "192ms"}, "throughput": "14.66 req/sec"}, "updateProfile": {"successRate": "100.00%", "successes": 10, "failures": 0, "responseTime": {"min": "48ms", "max": "240ms", "avg": "164ms", "p95": "240ms", "p99": "240ms"}, "throughput": "14.66 req/sec"}, "submitAssessment": {"successRate": "0.00%", "successes": 0, "failures": 10, "responseTime": {"min": "55ms", "max": "107ms", "avg": "82ms", "p95": "107ms", "p99": "107ms"}, "throughput": "14.66 req/sec"}, "websocketConnection": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "assessmentCompletion": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "checkResults": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}, "deleteAccount": {"successRate": "0.00%", "successes": 0, "failures": 0, "responseTime": {"min": "0ms", "max": "0ms", "avg": "0ms", "p95": "0ms", "p99": "0ms"}, "throughput": "0.00 req/sec"}}, "rawResults": [{"status": "fulfilled", "value": {"success": false, "userId": 1, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 2, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 3, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 4, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 5, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 6, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 7, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 8, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 9, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}, {"status": "fulfilled", "value": {"success": false, "userId": 10, "error": "Assessment submission failed: {\"success\":false,\"error\":{\"code\":\"VALIDATION_ERROR\",\"message\":\"Validation failed\",\"details\":{\"riasec\":\"RIASEC assessment data is required\",\"ocean\":\"OCEAN assessment data is required\",\"viaIs\":\"VIA-IS assessment data is required\",\"assessmentName\":\"Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment\"}}}"}}]}