{"name": "atma-backend-testing", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend Testing Suite", "main": "e2e-load-test.js", "scripts": {"test": "node e2e-load-test.js", "test:load": "node e2e-load-test.js", "test:small": "node e2e-load-test.js --users 10", "test:medium": "node e2e-load-test.js --users 25", "test:large": "node e2e-load-test.js --users 100", "test:custom": "node e2e-load-test.js", "health": "node health-check.js", "health:detailed": "node health-check.js --detailed", "health:json": "node health-check.js --json", "scenarios": "node test-scenarios.js", "scenarios:all": "node test-scenarios.js all", "scenarios:progressive": "node test-scenarios.js progressive", "scenarios:stress": "node test-scenarios.js stress-test", "smoke": "node test-scenarios.js smoke", "help": "node e2e-load-test.js --help"}, "keywords": ["testing", "e2e", "load-testing", "api-testing", "atma", "backend", "websocket"], "author": "ATMA Development Team", "license": "ISC", "dependencies": {"axios": "^1.10.0", "socket.io-client": "^4.7.5", "uuid": "^11.1.0"}, "engines": {"node": ">=16.0.0"}}